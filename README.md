# Graphic Designer Portfolio

A modern, responsive portfolio website for graphic designers built with HTML, CSS (Tailwind CSS), and JavaScript.

## Features

### 🎨 Design
- **Modern UI/UX**: Clean, professional design with smooth animations
- **Dark/Light Mode**: Toggle between themes with smooth transitions
- **Responsive Design**: Optimized for all screen sizes (mobile, tablet, desktop)
- **Gradient Effects**: Beautiful gradient backgrounds and text effects
- **Smooth Animations**: CSS animations and JavaScript interactions

### 🚀 Functionality
- **Portfolio Filtering**: Filter projects by category (Branding, Web Design, Print, Illustration)
- **Smooth Scrolling**: Seamless navigation between sections
- **Contact Form**: Interactive contact form with validation
- **Mobile Menu**: Responsive navigation for mobile devices
- **Scroll Animations**: Elements animate as they come into view
- **Parallax Effects**: Subtle parallax scrolling for enhanced visual appeal

### 📱 Sections
1. **Hero Section**: Eye-catching introduction with animated elements
2. **About**: Personal information and skills showcase
3. **Portfolio**: Filterable project gallery
4. **Services**: List of offered design services
5. **Contact**: Contact information and form
6. **Footer**: Social links and copyright

## Technologies Used

- **HTML5**: Semantic markup structure
- **Tailwind CSS**: Utility-first CSS framework
- **Vanilla JavaScript**: Interactive functionality
- **Font Awesome**: Icons
- **Google Fonts**: Typography (Inter & Playfair Display)

## File Structure

```
graphic-port/
├── index.html          # Main HTML file
├── styles.css          # Custom CSS styles and animations
├── script.js           # JavaScript functionality
└── README.md           # Documentation
```

## Customization

### Colors
The portfolio uses a purple-pink gradient theme. To change colors, update the Tailwind classes in:
- `index.html`: Update gradient classes (from-purple-600, to-pink-600, etc.)
- `styles.css`: Update custom gradient definitions

### Content
1. **Personal Information**: Update name, title, and contact details in `index.html`
2. **Portfolio Items**: Replace placeholder projects with actual work
3. **Services**: Modify the services section to match your offerings
4. **About Section**: Update the about text and skills

### Images
Replace placeholder content with actual images:
- Profile photo in the About section
- Portfolio project images
- Add your own favicon

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Performance Features

- **Optimized Loading**: Efficient CSS and JavaScript loading
- **Smooth Animations**: Hardware-accelerated CSS transitions
- **Responsive Images**: Optimized for different screen sizes
- **Minimal Dependencies**: Only essential external resources

## Getting Started

1. Clone or download the files
2. Open `index.html` in a web browser
3. Customize the content to match your portfolio
4. Deploy to your preferred hosting platform

## Deployment

This portfolio can be deployed to any static hosting service:
- GitHub Pages
- Netlify
- Vercel
- Firebase Hosting
- Traditional web hosting

## License

This project is open source and available under the MIT License.

---

**Note**: This is a template portfolio. Replace all placeholder content with your actual work and information.
